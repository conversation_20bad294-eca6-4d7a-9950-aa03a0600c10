import {Header, <PERSON>} from '@/components/home';

type PageLayoutProps = {
  children: React.ReactNode;
};

export default function PageLayout({children}: PageLayoutProps) {
  return (
    <div className="min-h-screen bg-forever-fest-gradient">
      <Header />
      <Navigation />

      {/* Main Content Container with 600px max width */}
      <div className="max-w-[600px] mx-auto">
        {children}
      </div>
    </div>
  );
}
